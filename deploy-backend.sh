#!/bin/bash

echo "🚀 Preparing Laravel Backend for Hostinger Deployment"
echo "===================================================="

# Navigate to Laravel directory
cd park-and-rent-api

# Set production environment
echo "📝 Setting production environment..."
cp .env.production .env

# Install/Update Composer dependencies (production only)
echo "📦 Installing Composer dependencies..."
composer install --optimize-autoloader --no-dev

# Clear and cache configurations
echo "🧹 Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo "⚡ Optimizing for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate storage link
echo "🔗 Creating storage link..."
php artisan storage:link

# Set proper permissions
echo "🔐 Setting permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Create deployment package
echo "📦 Creating backend deployment package..."
cd ..
zip -r backend-production.zip park-and-rent-api \
    --exclude="park-and-rent-api/node_modules/*" \
    --exclude="park-and-rent-api/.git/*" \
    --exclude="park-and-rent-api/tests/*" \
    --exclude="park-and-rent-api/.env.example" \
    --exclude="park-and-rent-api/README.md"

echo "✅ Backend prepared successfully!"
echo ""
echo "📁 Upload backend-production.zip to your Hostinger account"
echo "🗂️ Extract it to your domain's root directory"
echo ""
echo "🔧 Next steps on Hostinger:"
echo "   1. Extract backend-production.zip"
echo "   2. Move park-and-rent-api/public/* to public_html/"
echo "   3. Move park-and-rent-api/ to a folder outside public_html/"
echo "   4. Update public_html/index.php paths"
echo "   5. Run database migrations"
echo ""
echo "🧪 Test API endpoint: https://ebisera.com/api/cars"
