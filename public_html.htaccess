<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle API routes - redirect to <PERSON>vel backend
    RewriteCond %{REQUEST_URI} ^/api/(.*)$
    RewriteRule ^api/(.*)$ /index.php [L,QSA]
    
    # Handle React Router - serve index.html for frontend routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule . /index.html [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# CORS Headers for API
<IfModule mod_headers.c>
    SetEnvIf Origin "https://ebisera.com" CORS_ALLOW_ORIGIN=$0
    Header always set Access-Control-Allow-Origin %{CORS_ALLOW_ORIGIN}e env=CORS_ALLOW_ORIGIN
    Header always set Access-Control-Allow-Headers "origin, x-requested-with, content-type, authorization, accept"
    Header always set Access-Control-Allow-Methods "PUT, GET, POST, DELETE, OPTIONS, PATCH"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Handle preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Disable directory browsing
Options -Indexes

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>
