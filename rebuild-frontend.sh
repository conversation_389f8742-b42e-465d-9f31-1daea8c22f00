#!/bin/bash

echo "🚀 Rebuilding Frontend for Production"
echo "===================================="

# Set production environment
echo "📝 Setting production environment..."
echo "VITE_API_URL=https://ebisera.com/api" > .env
echo "VITE_APP_NAME=\"Park & Rent\"" >> .env
echo "VITE_APP_ENV=production" >> .env

# Install dependencies (if needed)
echo "📦 Installing dependencies..."
npm install

# Build for production
echo "🏗️ Building for production..."
npm run build

# Create deployment package
echo "📦 Creating deployment package..."
cd dist
zip -r ../frontend-production.zip .
cd ..

echo "✅ Frontend rebuilt successfully!"
echo ""
echo "📁 Upload frontend-production.zip to your public_html/ directory"
echo "🗂️ Extract it and replace existing files"
echo ""
echo "🧪 After upload, test:"
echo "   - https://ebisera.com (homepage)"
echo "   - https://ebisera.com/cars (cars page)"
echo ""
echo "🔧 The frontend will now use: https://ebisera.com/api"
