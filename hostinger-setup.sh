#!/bin/bash

echo "🗄️ Hostinger Database Setup Script"
echo "=================================="
echo ""
echo "⚠️  Run this script on your Hostinger server via SSH"
echo "📁 Make sure you're in the laravel-app directory"
echo ""

# Check if we're in Laravel directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: Please run this script from the laravel-app directory"
    echo "   cd laravel-app"
    echo "   bash hostinger-setup.sh"
    exit 1
fi

echo "🔧 Setting up Laravel application on Hostinger..."
echo ""

# Generate application key if needed
echo "🔑 Generating application key..."
php artisan key:generate --force

# Run database migrations
echo "🗄️ Running database migrations..."
php artisan migrate --force

# Seed the database
echo "🌱 Seeding database with initial data..."
php artisan db:seed --force

# Create storage link
echo "🔗 Creating storage symbolic link..."
php artisan storage:link

# Clear and cache everything
echo "🧹 Clearing and caching configurations..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set proper permissions
echo "🔐 Setting file permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public

echo ""
echo "✅ Hostinger setup complete!"
echo ""
echo "🧪 Test your application:"
echo "   • Frontend: https://ebisera.com"
echo "   • API: https://ebisera.com/api/cars"
echo "   • Admin: https://ebisera.com/admin"
echo ""
echo "🔍 If you encounter issues, check:"
echo "   • Laravel logs: storage/logs/laravel.log"
echo "   • Apache error logs in Hostinger control panel"
echo "   • Database connection in .env file"
echo ""
echo "📞 Support: 0788613669 | <EMAIL>"
