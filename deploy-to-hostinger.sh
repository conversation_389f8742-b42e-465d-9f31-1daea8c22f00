#!/bin/bash

echo "🚀 Complete Hostinger Deployment Script"
echo "======================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "park-and-rent-api" ]; then
    echo "❌ Error: Please run this script from the park_and_rent root directory"
    exit 1
fi

echo "📋 This script will:"
echo "   1. Build the React frontend for production"
echo "   2. Prepare the Laravel backend for production"
echo "   3. Create deployment packages"
echo "   4. Provide upload instructions"
echo ""

read -p "🤔 Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

echo ""
echo "🎯 Step 1: Building Frontend..."
echo "==============================="

# Set production environment for frontend
echo "📝 Setting frontend production environment..."
echo "VITE_API_URL=https://ebisera.com/api" > .env
echo "VITE_APP_NAME=\"Park & Rent\"" >> .env
echo "VITE_APP_ENV=production" >> .env

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

# Build frontend
echo "🏗️ Building frontend for production..."
npm run build

# Create frontend package
echo "📦 Creating frontend deployment package..."
cd dist
zip -r ../frontend-production.zip .
cd ..

echo "✅ Frontend build complete!"
echo ""

echo "🎯 Step 2: Preparing Backend..."
echo "==============================="

# Navigate to Laravel directory
cd park-and-rent-api

# Set production environment
echo "📝 Setting backend production environment..."
cp .env.production .env

# Install Composer dependencies
echo "📦 Installing Composer dependencies..."
composer install --optimize-autoloader --no-dev

# Clear and optimize Laravel
echo "🧹 Clearing caches and optimizing..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
echo "🔐 Setting permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Go back to root
cd ..

# Create backend package
echo "📦 Creating backend deployment package..."
zip -r backend-production.zip park-and-rent-api \
    --exclude="park-and-rent-api/node_modules/*" \
    --exclude="park-and-rent-api/.git/*" \
    --exclude="park-and-rent-api/tests/*" \
    --exclude="park-and-rent-api/.env.example" \
    --exclude="park-and-rent-api/README.md"

echo "✅ Backend preparation complete!"
echo ""

echo "🎯 Step 3: Deployment Packages Created"
echo "======================================"
echo ""
echo "📁 Files created:"
echo "   ✅ frontend-production.zip (React app)"
echo "   ✅ backend-production.zip (Laravel API)"
echo "   ✅ public_html.htaccess (Apache configuration)"
echo "   ✅ HOSTINGER_DEPLOYMENT_GUIDE.md (Detailed guide)"
echo ""

echo "🚀 Next Steps - Upload to Hostinger:"
echo "===================================="
echo ""
echo "1. 📤 Login to Hostinger File Manager"
echo ""
echo "2. 🗂️ Upload Backend:"
echo "   • Upload backend-production.zip to your account root"
echo "   • Extract it"
echo "   • Move park-and-rent-api/public/* to public_html/"
echo "   • Move park-and-rent-api/ to laravel-app/ (outside public_html)"
echo ""
echo "3. 🗂️ Upload Frontend:"
echo "   • Upload frontend-production.zip to public_html/"
echo "   • Extract it in public_html/"
echo ""
echo "4. ⚙️ Configure:"
echo "   • Copy public_html.htaccess to public_html/.htaccess"
echo "   • Update public_html/index.php paths (see guide)"
echo "   • Set up database and run migrations"
echo ""
echo "5. 🧪 Test:"
echo "   • Frontend: https://ebisera.com"
echo "   • API: https://ebisera.com/api/cars"
echo ""
echo "📖 For detailed instructions, see: HOSTINGER_DEPLOYMENT_GUIDE.md"
echo ""
echo "📞 Support: ********** | <EMAIL>"
echo ""
echo "✅ Deployment packages ready for upload!"
