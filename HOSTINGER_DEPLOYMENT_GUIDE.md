# 🚀 Hostinger Deployment Guide for Park & Rent

## Overview
This guide will help you deploy your full-stack Park & Rent application (React frontend + Laravel API backend) to Hostinger hosting.

## Prerequisites
- Hostinger hosting account with PHP 8.2+ support
- Domain: ebisera.com (already configured)
- MySQL database access
- SSH/File Manager access

## 📁 Project Structure
```
park_and_rent/
├── src/                    # React frontend source
├── park-and-rent-api/      # Laravel API backend
├── public/                 # Frontend build output
├── rebuild-frontend.sh     # Frontend deployment script
├── deploy-backend.sh       # Backend deployment script
└── HOSTINGER_DEPLOYMENT_GUIDE.md
```

## 🎯 Deployment Steps

### Step 1: Prepare Local Environment

1. **Update production configurations** (if needed):
   ```bash
   # Update park-and-rent-api/.env.production with your Hostinger database details
   # Update frontend API URL in rebuild-frontend.sh if needed
   ```

### Step 2: Build Frontend for Production

1. **Run the frontend build script**:
   ```bash
   ./rebuild-frontend.sh
   ```
   This will:
   - Set production environment variables
   - Install dependencies
   - Build the React app
   - Create `frontend-production.zip`

### Step 3: Prepare Backend for Production

1. **Run the backend deployment script**:
   ```bash
   ./deploy-backend.sh
   ```
   This will:
   - Set production environment
   - Install Composer dependencies
   - Optimize Laravel for production
   - Create `backend-production.zip`

### Step 4: Upload to Hostinger

#### 4.1 Upload Backend
1. **Login to Hostinger File Manager** or use FTP
2. **Upload `backend-production.zip`** to your account root
3. **Extract the zip file**
4. **Move Laravel files**:
   ```
   # Move public files to public_html
   park-and-rent-api/public/* → public_html/
   
   # Move Laravel app outside public_html for security
   park-and-rent-api/ → laravel-app/
   ```

#### 4.2 Update Laravel Paths
1. **Edit `public_html/index.php`**:
   ```php
   <?php
   
   use Illuminate\Foundation\Application;
   use Illuminate\Http\Request;
   
   define('LARAVEL_START', microtime(true));
   
   // Determine if the application is in maintenance mode...
   if (file_exists($maintenance = __DIR__.'/../laravel-app/storage/framework/maintenance.php')) {
       require $maintenance;
   }
   
   // Register the Composer autoloader...
   require __DIR__.'/../laravel-app/vendor/autoload.php';
   
   // Bootstrap Laravel and handle the request...
   /** @var Application $app */
   $app = require_once __DIR__.'/../laravel-app/bootstrap/app.php';
   
   $app->handleRequest(Request::capture());
   ```

#### 4.3 Upload Frontend
1. **Upload `frontend-production.zip`** to `public_html/`
2. **Extract the zip file** in `public_html/`
3. **The frontend files should be directly in `public_html/`**

### Step 5: Configure Hostinger Environment

#### 5.1 Database Setup
1. **Create MySQL database** in Hostinger control panel
2. **Update database credentials** in `laravel-app/.env`:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_database_user
   DB_PASSWORD=your_database_password
   ```

#### 5.2 Run Database Migrations
1. **Access SSH** or use Hostinger's terminal
2. **Navigate to Laravel directory**:
   ```bash
   cd laravel-app
   php artisan migrate --force
   php artisan db:seed --force
   ```

#### 5.3 Set File Permissions
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public
```

#### 5.4 Create Storage Link
```bash
cd laravel-app
php artisan storage:link
```

### Step 6: Configure .htaccess for Frontend Routing

1. **Create/Update `public_html/.htaccess`**:
   ```apache
   <IfModule mod_rewrite.c>
       RewriteEngine On
       
       # Handle API routes - redirect to Laravel
       RewriteCond %{REQUEST_URI} ^/api/(.*)$
       RewriteRule ^api/(.*)$ /index.php [L,QSA]
       
       # Handle React Router - serve index.html for frontend routes
       RewriteCond %{REQUEST_FILENAME} !-f
       RewriteCond %{REQUEST_FILENAME} !-d
       RewriteCond %{REQUEST_URI} !^/api/
       RewriteRule . /index.html [L]
   </IfModule>
   
   # Security Headers
   <IfModule mod_headers.c>
       Header always set X-Content-Type-Options nosniff
       Header always set X-Frame-Options DENY
       Header always set X-XSS-Protection "1; mode=block"
       Header always set Referrer-Policy "strict-origin-when-cross-origin"
   </IfModule>
   
   # CORS Headers for API
   <IfModule mod_headers.c>
       SetEnvIf Origin "https://ebisera.com" CORS_ALLOW_ORIGIN=$0
       Header always set Access-Control-Allow-Origin %{CORS_ALLOW_ORIGIN}e env=CORS_ALLOW_ORIGIN
       Header always set Access-Control-Allow-Headers "origin, x-requested-with, content-type, authorization, accept"
       Header always set Access-Control-Allow-Methods "PUT, GET, POST, DELETE, OPTIONS, PATCH"
       Header always set Access-Control-Allow-Credentials "true"
   </IfModule>
   
   # Handle preflight requests
   RewriteCond %{REQUEST_METHOD} OPTIONS
   RewriteRule ^(.*)$ $1 [R=200,L]
   ```

## 🧪 Testing Your Deployment

### Frontend Tests
- ✅ Homepage: `https://ebisera.com`
- ✅ Cars page: `https://ebisera.com/cars`
- ✅ Login page: `https://ebisera.com/login`
- ✅ React Router navigation works

### Backend API Tests
- ✅ API health: `https://ebisera.com/api/cars`
- ✅ Authentication: `https://ebisera.com/api/login`
- ✅ Database connection working
- ✅ File uploads working

### Integration Tests
- ✅ Frontend can communicate with API
- ✅ Authentication flow works
- ✅ Image uploads work
- ✅ All CRUD operations work

## 🔧 Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check Laravel logs: `laravel-app/storage/logs/laravel.log`
   - Verify file permissions
   - Check .env configuration

2. **API Routes Not Working**
   - Verify .htaccess configuration
   - Check mod_rewrite is enabled
   - Verify Laravel routes are cached

3. **CORS Issues**
   - Update CORS headers in .htaccess
   - Check SANCTUM_STATEFUL_DOMAINS in .env
   - Verify frontend API URL

4. **Database Connection Issues**
   - Verify database credentials
   - Check database server status
   - Test connection manually

5. **Frontend Routes Not Working**
   - Verify .htaccess rewrite rules
   - Check React Router configuration
   - Ensure index.html is in public_html root

## 📞 Support Contacts
- Phone: 0788613669
- Email: <EMAIL>

## 🔄 Future Deployments

For future updates:

1. **Frontend updates**: Run `./rebuild-frontend.sh` and upload new files
2. **Backend updates**: Run `./deploy-backend.sh` and upload new files
3. **Database changes**: Run migrations via SSH
4. **Environment changes**: Update .env files directly on server

## 📝 Notes
- Always backup your database before major updates
- Test changes on a staging environment first
- Monitor error logs after deployment
- Keep your local and production .env files in sync
